# Git Manager 配置文件
# 此文件用于自定义 git-manager.sh 脚本的行为

# 默认提交消息模板
DEFAULT_COMMIT_TEMPLATE="自动提交 - $(date '+%Y-%m-%d %H:%M:%S')"

# 默认分支名称
DEFAULT_BRANCH="main"

# 自动推送设置
AUTO_PUSH=true

# 备份分支前缀
BACKUP_PREFIX="backup"

# 日志显示条数
DEFAULT_LOG_COUNT=10

# 颜色主题 (可选: default, dark, light, minimal)
COLOR_THEME="default"

# 是否显示详细输出
VERBOSE=true

# 是否在危险操作前确认
CONFIRM_DANGEROUS_OPERATIONS=true

# 自定义别名
# 格式: ALIAS_名称="实际命令"
ALIAS_st="status"
ALIAS_ci="commit"
ALIAS_co="checkout"
ALIAS_br="branch"
ALIAS_unstage="reset"

# 忽略的文件模式（用于快速添加时排除）
IGNORE_PATTERNS=(
    "*.log"
    "*.tmp"
    ".DS_Store"
    "node_modules/"
    ".env.local"
)

# 预定义的提交消息类型
COMMIT_TYPES=(
    "feat: 新功能"
    "fix: 修复bug"
    "docs: 文档更新"
    "style: 代码格式调整"
    "refactor: 代码重构"
    "test: 测试相关"
    "chore: 构建过程或辅助工具的变动"
)

# 自动标签格式
TAG_FORMAT="v%s"  # %s 将被版本号替换

# 发布分支名称
RELEASE_BRANCH="release"

# 开发分支名称
DEVELOP_BRANCH="develop"

# 功能分支前缀
FEATURE_PREFIX="feature/"

# 修复分支前缀
HOTFIX_PREFIX="hotfix/"

# 远程仓库名称
REMOTE_NAME="origin"

# 是否在推送前自动拉取
AUTO_PULL_BEFORE_PUSH=true

# 是否在合并前自动备份
AUTO_BACKUP_BEFORE_MERGE=true

# 清理操作的安全模式
SAFE_CLEAN=true

# 最大日志显示条数
MAX_LOG_COUNT=100

# 是否显示分支图形
SHOW_BRANCH_GRAPH=true

# 默认编辑器（用于交互式操作）
DEFAULT_EDITOR="nano"

# 是否启用Git钩子
ENABLE_HOOKS=true

# 自定义钩子目录
HOOKS_DIR=".git/hooks"

# 是否在状态显示中包含未跟踪文件
SHOW_UNTRACKED=true

# 是否在状态显示中包含忽略文件
SHOW_IGNORED=false

# 差异显示的上下文行数
DIFF_CONTEXT_LINES=3

# 是否使用彩色输出
USE_COLORS=true

# 是否显示操作时间
SHOW_TIMESTAMPS=true

# 是否启用自动完成
ENABLE_AUTOCOMPLETE=true

# 配置文件版本
CONFIG_VERSION="1.0"
