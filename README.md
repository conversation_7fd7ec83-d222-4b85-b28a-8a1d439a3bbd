# 🌞 VPP-AI 新能源微网决策系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![API Status](https://img.shields.io/badge/API-Running-brightgreen.svg)](http://localhost:8000)

> 基于人工智能的光伏发电预测和优化决策系统

## 📋 项目概述

VPP-AI（Virtual Power Plant with AI）是一个基于人工智能的新能源微网智能决策系统，专为光伏储能项目的经济收益最大化而设计。系统集成了NASA POWER天气数据API和多种机器学习模型，能够为任意地理位置提供高精度的光伏发电预测，实现经济收益最大化的数字孪生系统。

**🎉 当前状态**: 光伏发电预测模块已完成开发并可运行！

## 核心功能

### 🌞 光伏发电预测模块
- 基于历史光照数据、天气预报和实时光照情况的AI预测
- 支持多种预测模型（LSTM、Prophet、XGBoost等）
- 考虑光伏板朝向、效率、遮挡等因素
- 提供功率和发电量的短期和中期预测

### 🔋 储能管理模块
- 智能充放电策略优化
- 电池状态监控和健康管理
- 多种储能技术支持（锂电池、铅酸电池等）
- 考虑电池寿命和效率的优化算法

### ⚡ 负荷预测模块
- 基于历史用电数据的负荷预测
- 支持多种负荷类型（空调、照明、生产设备、电动汽车充电等）
- 人工输入用电计划的集成
- 负荷灵活性分析和优化

### 💰 电网交易模块
- 分时电价分析和预测
- 上网售电策略优化
- 电力市场交易决策支持
- 收益最大化算法

### 🏭 数字孪生仿真模块
- 设备数字化建模
- 实时系统仿真
- 多场景分析和对比
- 设备性能监控和诊断

### 🧠 决策优化引擎
- 多目标优化算法
- 实时决策支持
- 风险评估和管理
- 经济收益最大化

## 技术架构

### 后端技术栈
- **框架**: FastAPI + SQLAlchemy + Pydantic
- **数据库**: PostgreSQL + Redis + MongoDB
- **AI/ML**: TensorFlow + PyTorch + Scikit-learn
- **优化**: CVXPY + PuLP
- **异步**: Celery + Redis

### 前端技术栈
- **框架**: React + TypeScript
- **可视化**: Plotly + D3.js + ECharts
- **UI组件**: Ant Design
- **状态管理**: Redux Toolkit

### 部署架构
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes（可选）
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 项目结构

```
VPP-AI/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   ├── database.py    # 数据库管理
│   │   ├── logger.py      # 日志管理
│   │   └── exceptions.py  # 异常处理
│   ├── models/            # 数据模型
│   │   ├── solar.py       # 光伏模型
│   │   ├── storage.py     # 储能模型
│   │   ├── load.py        # 负荷模型
│   │   ├── grid.py        # 电网模型
│   │   └── weather.py     # 天气模型
│   ├── services/          # 业务服务
│   │   ├── prediction/    # 预测服务
│   │   ├── optimization/  # 优化服务
│   │   ├── simulation/    # 仿真服务
│   │   └── monitoring/    # 监控服务
│   ├── api/               # API接口
│   │   ├── routes/        # 路由定义
│   │   ├── schemas/       # 数据模式
│   │   └── middleware/    # 中间件
│   └── utils/             # 工具函数
├── tests/                 # 测试代码
│   ├── unit/             # 单元测试
│   └── integration/      # 集成测试
├── data/                 # 数据目录
│   ├── raw/              # 原始数据
│   ├── processed/        # 处理后数据
│   └── models/           # 训练模型
├── docs/                 # 文档
├── config/               # 配置文件
├── logs/                 # 日志文件
├── main.py              # 主入口
├── config.yaml          # 主配置文件
└── requirements.txt     # 依赖包
```

## 开发计划

### 第一阶段：基础架构（已完成）
- [x] 项目结构搭建
- [x] 核心模块开发（配置、数据库、日志、异常处理）
- [x] 数据模型设计（光伏、储能、负荷、电网、天气）
- [x] 基础工具和依赖配置

### 第二阶段：数据层开发（进行中）
- [ ] 数据库表结构创建
- [ ] 数据访问层（DAO）开发
- [ ] 数据验证和清洗
- [ ] 时序数据存储优化

### 第三阶段：AI预测模块
- [ ] 光伏发电预测模型
- [ ] 负荷预测模型
- [ ] 天气数据集成
- [ ] 模型训练和评估框架

### 第四阶段：优化引擎
- [ ] 储能充放电优化
- [ ] 电网交易策略优化
- [ ] 多目标优化算法
- [ ] 实时决策引擎

### 第五阶段：数字孪生
- [ ] 设备建模框架
- [ ] 实时仿真引擎
- [ ] 场景分析工具
- [ ] 性能监控系统

### 第六阶段：API和界面
- [ ] RESTful API开发
- [ ] Web管理界面
- [ ] 移动端应用
- [ ] 数据可视化

### 第七阶段：部署和运维
- [ ] Docker容器化
- [ ] CI/CD流水线
- [ ] 监控和告警
- [ ] 性能优化

## 应用场景

- **工业园区**: 大型工业用电负荷的光伏储能优化
- **商业综合体**: 商业建筑的能源管理和成本优化
- **住宅小区**: 居民用电的光伏储能系统优化
- **数据中心**: 高能耗设施的绿色能源解决方案

## 快速开始

### 环境要求
- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- MongoDB 5+

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置数据库
```bash
# 创建PostgreSQL数据库
createdb vpp_ai

# 启动Redis
redis-server

# 启动MongoDB
mongod
```

### 运行系统
```bash
python main.py
```

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License

## 联系方式

项目开发者：新能源从业者
开发时间：2025年6月

---

**注意**: 这是一个正在开发中的项目，功能将逐步完善。
